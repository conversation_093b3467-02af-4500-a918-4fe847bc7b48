from typing import List, Dict, Any, Optional, Tuple, Union
import json
import math
import random
import datetime
import os
import json
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple, Union, Callable
import numpy as np
import statistics
from dataclasses import dataclass, field
from enum import Enum
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Type aliases
FeatureVector = List[float]
PerformanceMetrics = Dict[str, float]
Strategy = str
Workload = List[Any]

# Constants
DEFAULT_STRATEGIES = ["cpu_intensive", "memory_intensive", "io_intensive", "gpu_intensive", "balanced"]
MAX_WORKLOAD_HISTORY = 1000
EMBEDDING_DIM = 384  # Dimension for sentence transformer embeddings

# Optional imports with fallbacks
try:
    import psutil
except ImportError:
    psutil = None

try:
    import GPUtil
except ImportError:
    GPUtil = None

try:
    from sklearn.cluster import KMeans
    from sklearn.preprocessing import StandardScaler
    from sklearn.metrics import silhouette_score
    sklearn_available = True
except ImportError:
    sklearn_available = False
    class MockStandardScaler:
        def fit_transform(self, X):
            return X
        def transform(self, X):
            return X
    StandardScaler = MockStandardScaler

    class MockKMeans:
        def __init__(self, *args, **kwargs):
            pass
        def fit(self, X):
            return self
        def predict(self, X):
            return [0] * len(X)
    KMeans = MockKMeans

# Gym and RL related imports
try:
    import gym
    from gym import spaces
    gym_available = True
except ImportError:
    gym_available = False
    # Mock spaces for when gym is not available
    class spaces:
        class Box:
            def __init__(self, low, high, shape, dtype):
                self.low = low
                self.high = high
                self.shape = shape
                self.dtype = dtype

        class Discrete:
            def __init__(self, n):
                self.n = n

# XGBoost imports
try:
    import xgboost as xgb
    xgb_available = True
except ImportError:
    xgb_available = False
    xgb = None

# Text processing imports
try:
    import textstat
    textstat_available = True
except ImportError:
    textstat_available = False
    textstat = None

# SHAP explainability
try:
    import shap
    shap_available = True
except ImportError:
    shap_available = False
    shap = None

# TensorFlow related imports
try:
    import tensorflow as tf
    from tensorflow.keras.applications import EfficientNetV2B0
    from tensorflow.keras.layers import Dense, GlobalAveragePooling2D, LSTM, Attention, Input, Concatenate
    from tensorflow.keras.models import Model, Sequential
    from tensorflow.keras.optimizers import Adam
    tf_available = True
except (ImportError, AttributeError) as e:
    print(f"Warning: TensorFlow not available or incompatible: {e}")
    print("Deep learning features will be disabled.")
    tf_available = False
    # Create mock TensorFlow objects
    tf = None

# Mock classes for optional dependencies
class MockNumpy:
    def __init__(self):
        self.float32 = None
        self.linalg = MockLinalg()

    def array(self, *args, **kwargs):
        return []

    def mean(self, *args, **kwargs):
        return 0.0

    def argmax(self, *args, **kwargs):
        return 0

    def reshape(self, *args, **kwargs):
        return []

class MockLinalg:
    def norm(self, *args, **kwargs):
        return 0.0

if 'np' not in globals():
    np = MockNumpy()

class MockStatistics:
    @staticmethod
    def mean(data):
        return sum(data) / len(data) if data else 0

    @staticmethod
    def median(data):
        if not data:
            return 0
        sorted_data = sorted(data)
        n = len(sorted_data)
        return (sorted_data[n//2] + sorted_data[-(n//2 + 1)]) / 2

    @staticmethod
    def stdev(data):
        if len(data) < 2:
            return 0.0
        avg = sum(data) / len(data)
        variance = sum((x - avg) ** 2 for x in data) / (len(data) - 1)
        return math.sqrt(variance)

# Handle optional dependencies gracefully
try:
    import numpy as np
except ImportError:
    print("Warning: numpy not available. Some functionality will be limited.")
    # Create a minimal numpy-like module with just enough functionality for basic initialization
    class MockNumpy:
        def __init__(self):
            pass
        def ndarray(self, *args, **kwargs):
            return []
    np = MockNumpy()

try:
    import psutil
except ImportError:
    print("Warning: psutil not available. Hardware monitoring will be limited.")
    psutil = None

try:
    import GPUtil
except ImportError:
    print("Warning: GPUtil not available. GPU monitoring will be disabled.")
    GPUtil = None

try:
    from sklearn.cluster import KMeans
    from sklearn.preprocessing import StandardScaler
    sklearn_available = True
except ImportError:
    print("Warning: scikit-learn not available. Advanced analytics will be disabled.")
    sklearn_available = False
    # Create mock classes
    class MockStandardScaler:
        def __init__(self):
            pass
        def fit_transform(self, X):
            return X
        def transform(self, X):
            return X
    StandardScaler = MockStandardScaler

    class MockKMeans:
        def __init__(self, *args, **kwargs):
            pass
        def fit(self, X):
            return self
        def predict(self, X):
            return [0] * len(X)
    KMeans = MockKMeans

try:
    import statistics
except ImportError:
    print("Warning: statistics module not available. Using simplified statistics.")
    # Create a minimal statistics module
    class MockStatistics:
        def mean(self, data):
            return sum(data) / len(data) if data else 0
        def median(self, data):
            sorted_data = sorted(data)
            n = len(sorted_data)
            if n % 2 == 0:
                return (sorted_data[n//2-1] + sorted_data[n//2]) / 2
            else:
                return sorted_data[n//2]
    statistics = MockStatistics()

class WorkloadEnv:
    def __init__(self, analyzer: 'WorkloadAnalyzer'):
        super(WorkloadEnv, self).__init__()
        self.analyzer = analyzer
        self.action_space = spaces.Discrete(5)  # 5 strategies
        self.observation_space = spaces.Box(low=0, high=1, shape=(6,), dtype=np.float32)

@dataclass
class WorkloadAnalysisResult:
    """Container for workload analysis results."""
    workload_type: str
    resource_requirements: Dict[str, float]
    recommended_strategy: str
    confidence: float = 0.0
    features: Optional[Dict[str, float]] = None
    similar_workloads: Optional[List[Dict[str, Any]]] = None

class WorkloadAnalyzer:
    """Analyzes workloads and recommends optimal execution strategies.

    This class uses machine learning to analyze workload characteristics and
    recommend the best execution strategy. It supports multiple ML models
    including deep learning, XGBoost, and reinforcement learning.
    """

    def __init__(self, learning_enabled: bool = True, learning_path: str = 'workload_learning.json'):
        """Initialize the WorkloadAnalyzer.

        Args:
            learning_enabled: Whether to enable learning from past workloads
            learning_path: Path to store/load learning data
        """
        self.learning_enabled = learning_enabled
        self.learning_path = Path(learning_path)
        self.learning_data = self._load_learning_data()
        self.scaler = StandardScaler()

        # Model placeholders
        self.transfer_model: Optional[Model] = None
        self.dl_model: Optional[Model] = None
        self.xgb_model: Optional[Any] = None
        self.rl_model: Optional[Any] = None
        self.embedder: Optional[Any] = None

        # Initialize learning models if enabled
        if self.learning_enabled:
            try:
                self._initialize_models()
            except Exception as e:
                logger.error(f"Failed to initialize ML models: {e}")
                self.learning_enabled = False

        # Initialize RL environment if gym is available
        self.env: Optional[WorkloadEnv] = WorkloadEnv(self) if gym_available else None

    def _initialize_models(self) -> None:
        """Initialize all learning models with transfer learning capabilities.

        This method initializes:
        - Sentence Transformer for text embeddings
        - Transfer learning model using EfficientNetV2
        - Deep learning model
        - XGBoost model (if available)
        - RL model (if gym is available)
        """
        # Initialize sentence transformer for text embeddings
        try:
            from sentence_transformers import SentenceTransformer
            self.embedder = SentenceTransformer('all-MiniLM-L6-v2')
            logger.info("Initialized sentence transformer model")
        except Exception as e:
            logger.error(f"Failed to initialize sentence transformer: {e}")
            self.embedder = None

        # Initialize transfer learning model if TensorFlow is available
        if tf_available:
            try:
                # Create base model
                base_model = EfficientNetV2B0(weights='imagenet', include_top=False)

                # Add custom layers
                x = base_model.output
                x = GlobalAveragePooling2D()(x)
                x = Dense(1024, activation='relu')(x)
                predictions = Dense(len(DEFAULT_STRATEGIES), activation='softmax')(x)

                # Create model
                self.transfer_model = Model(inputs=base_model.input, outputs=predictions)

                # Freeze base model layers
                for layer in base_model.layers:
                    layer.trainable = False

                # Compile the model
                self.transfer_model.compile(
                    optimizer=Adam(learning_rate=1e-3),
                    loss='categorical_crossentropy',
                    metrics=['accuracy']
                )
                logger.info("Initialized transfer learning model")

            except Exception as e:
                logger.error(f"Failed to initialize transfer learning model: {e}")
                self.transfer_model = None

        # Initialize deep learning model
        if tf_available:
            try:
                self._initialize_dl_model()
                logger.info("Initialized deep learning model")
            except Exception as e:
                logger.error(f"Failed to initialize deep learning model: {e}")
                self.dl_model = None

        # Initialize XGBoost model if available
        if xgb_available:
            try:
                self.xgb_model = xgb.XGBClassifier(
                    n_estimators=100,
                    max_depth=5,
                    learning_rate=0.1,
                    objective='multi:softprob',
                    num_class=len(DEFAULT_STRATEGIES)
                )
                logger.info("Initialized XGBoost model")
            except Exception as e:
                logger.error(f"Failed to initialize XGBoost model: {e}")
                self.xgb_model = None

    def _initialize_dl_model(self) -> None:
        """Initialize the deep learning model."""
        if not tf_available:
            return

        try:
            # Define input shape based on feature dimensions
            input_shape = (EMBEDDING_DIM,)  # Using embedding dimension as input shape

            # Create model architecture
            inputs = Input(shape=input_shape)
            x = Dense(256, activation='relu')(inputs)
            x = Dense(128, activation='relu')(x)
            x = Dense(64, activation='relu')(x)

            # Output layer with softmax for multi-class classification
            outputs = Dense(len(DEFAULT_STRATEGIES), activation='softmax')(x)

            # Create and compile model
            self.dl_model = Model(inputs=inputs, outputs=outputs)
            self.dl_model.compile(
                optimizer=Adam(learning_rate=1e-3),
                loss='sparse_categorical_crossentropy',
                metrics=['accuracy']
            )

        except Exception as e:
            logger.error(f"Error initializing deep learning model: {e}")
            self.dl_model = None

        # Initialize RL model if gym is available
        if gym_available and self.env is not None:
            try:
                from stable_baselines3 import PPO
                self.rl_model = PPO('MlpPolicy', self.env, verbose=1)
                logger.info("Initialized RL model with PPO")
            except ImportError:
                logger.warning("stable-baselines3 not available, RL will be disabled")
                self.rl_model = None
            except Exception as e:
                logger.error(f"Failed to initialize RL model: {e}")
                self.rl_model = None

    def _create_rl_env(self) -> Optional[Any]:
        """Create a reinforcement learning environment.

        Returns:
            Optional[Any]: The RL environment or None if gym is not available
        """
        if not gym_available or not tf_available:
            return None

        try:
            # Register the environment if not already registered
            if 'WorkloadEnv-v0' not in gym.envs.registry:
                gym.envs.register(
                    id='WorkloadEnv-v0',
                    entry_point=lambda: WorkloadEnv(self),
                    max_episode_steps=1000
                )

            # Create and return the environment
            return gym.make('WorkloadEnv-v0')

        except Exception as e:
            logger.error(f"Failed to create RL environment: {e}")
            return None

    def _get_transfer_prediction(self, features: np.ndarray) -> Dict[str, float]:
        """Get prediction from transfer learning model.

        Args:
            features: Input features for prediction

        Returns:
            Dictionary mapping strategy names to confidence scores
        """
        if self.transfer_model is None or not tf_available:
            return {strat: 0.0 for strat in DEFAULT_STRATEGIES}

        try:
            # Ensure features are in the correct shape for the model
            if len(features.shape) == 1:
                features = np.expand_dims(features, axis=0)

            # Get predictions
            predictions = self.transfer_model.predict(features, verbose=0)

            # Convert to dictionary of strategy -> confidence
            return {strat: float(conf) for strat, conf in zip(DEFAULT_STRATEGIES, predictions[0])}

        except Exception as e:
            logger.error(f"Error in transfer learning prediction: {e}")
            return {strat: 1.0/len(DEFAULT_STRATEGIES) for strat in DEFAULT_STRATEGIES}

    def _get_xgb_prediction(self, features: np.ndarray) -> Dict[str, float]:
        """Get prediction from XGBoost model.

        Args:
            features: Input features for prediction

        Returns:
            Dictionary mapping strategy names to confidence scores
        """
        if self.xgb_model is None or not xgb_available:
            return {strat: 0.0 for strat in DEFAULT_STRATEGIES}

        try:
            # Ensure features are in the correct shape for the model
            if len(features.shape) == 1:
                features = features.reshape(1, -1)

            # Get predictions
            predictions = self.xgb_model.predict_proba(features)

            # Convert to dictionary of strategy -> confidence
            return {strat: float(conf) for strat, conf in zip(DEFAULT_STRATEGIES, predictions[0])}

        except Exception as e:
            logger.error(f"Error in XGBoost prediction: {e}")
            return {strat: 1.0/len(DEFAULT_STRATEGIES) for strat in DEFAULT_STRATEGIES}

    def _get_dl_prediction(self, features: np.ndarray) -> Dict[str, float]:
        """Get prediction from deep learning model.

        Args:
            features: Input features for prediction

        Returns:
            Dictionary mapping strategy names to confidence scores
        """
        if self.dl_model is None or not tf_available:
            return {strat: 0.0 for strat in DEFAULT_STRATEGIES}

        try:
            # Ensure features are in the correct shape for the model
            if len(features.shape) == 1:
                features = np.expand_dims(features, axis=0)

            # Get predictions
            predictions = self.dl_model.predict(features, verbose=0)

            # Convert to dictionary of strategy -> confidence
            return {strat: float(conf) for strat, conf in zip(DEFAULT_STRATEGIES, predictions[0])}

        except Exception as e:
            logger.error(f"Error in deep learning prediction: {e}")
            return {strat: 1.0/len(DEFAULT_STRATEGIES) for strat in DEFAULT_STRATEGIES}

        return WorkloadEnv(self)

    def _load_learning_data(self) -> Dict:
        """Load historical workload data for learning."""
        if self.learning_path.exists():
            with open(self.learning_path, 'r') as f:
                return json.load(f)
        return {
            'workloads': [],
            'strategies': [],
            'performance': [],
            'resource_usage': [],
            'rl_data': [],
            'dl_data': [],
            'xgb_data': []
        }

    def _save_learning_data(self):
        """Save learning data to disk."""
        with open(self.learning_path, 'w') as f:
            json.dump(self.learning_data, f, indent=4)

    def analyze_workload(self, tasks: List[Any]) -> Dict[str, Any]:
        """Analyze workload characteristics."""
        # Get task characteristics
        prompt_lengths = [len(task[0]) for task in tasks]  # Assuming task[0] is the prompt
        angles = [len(task[1].split()) if task[1] else 1 for task in tasks]  # Assuming task[1] is the angle

        # Calculate statistics
        workload_stats = {
            'size': {
                'mean': statistics.mean(prompt_lengths),
                'std': statistics.stdev(prompt_lengths),
                'max': max(prompt_lengths),
                'min': min(prompt_lengths)
            },
            'complexity': {
                'mean_angle': statistics.mean(angles),
                'task_count': len(tasks)
            }
        }

        # Get resource usage
        resource_stats = self._get_resource_usage()

        # Get historical performance data
        performance_stats = self._get_performance_stats(tasks)

        return {
            'workload_stats': workload_stats,
            'resource_stats': resource_stats,
            'performance_stats': performance_stats,
            'timestamp': self._get_current_time()
        }

    def _get_resource_usage(self) -> Dict[str, float]:
        """Get current system resource usage."""
        ram = psutil.virtual_memory()
        cpu = psutil.cpu_percent(interval=0.1)
        gpu_usage = 0.0
        try:
            gpus = GPUtil.getGPUs()
            gpu_usage = max(gpu.memoryUtil for gpu in gpus)
        except:
            pass

        return {
            'ram': ram.percent,
            'cpu': cpu,
            'gpu': gpu_usage * 100,
            'swap': psutil.swap_memory().percent
        }

    def _get_performance_stats(self, tasks: List[Any]) -> Dict[str, Any]:
        """Get performance statistics for similar workloads."""
        if not self.learning_data['workloads']:
            return {'avg_time': 0, 'avg_memory': 0}

        # Find similar workloads using clustering
        current_workload = self._extract_workload_features(tasks)
        clusters = self._cluster_workloads()

        # Get stats from the most similar cluster
        cluster_idx = self._find_closest_cluster(current_workload, clusters)
        similar_workloads = [w for i, w in enumerate(self.learning_data['workloads'])
                           if clusters[i] == cluster_idx]

        if not similar_workloads:
            return {'avg_time': 0, 'avg_memory': 0}

        times = [self.learning_data['performance'][i]['time']
                for i in range(len(self.learning_data['workloads']))
                if clusters[i] == cluster_idx]
        memories = [self.learning_data['performance'][i]['memory']
                   for i in range(len(self.learning_data['workloads']))
                   if clusters[i] == cluster_idx]

        return {
            'avg_time': statistics.mean(times) if times else 0,
            'avg_memory': statistics.mean(memories) if memories else 0,
            'std_time': statistics.stdev(times) if len(times) > 1 else 0,
            'std_memory': statistics.stdev(memories) if len(memories) > 1 else 0
        }

    def _extract_workload_features(self, tasks: List[Any]) -> List[float]:
        """Extract sophisticated features from workload."""
        # Basic features
        prompt_lengths = [len(task[0]) for task in tasks]
        angles = [len(task[1].split()) if task[1] else 1 for task in tasks]

        # Advanced text features
        from sklearn.feature_extraction.text import CountVectorizer
        from sklearn.decomposition import TruncatedSVD
        import numpy as np

        # TF-IDF features
        prompts = [task[0] for task in tasks]
        vectorizer = CountVectorizer()
        tfidf_matrix = vectorizer.fit_transform(prompts)

        # Topic modeling (LSA)
        svd = TruncatedSVD(n_components=3)
        lsa_matrix = svd.fit_transform(tfidf_matrix)

        # Text complexity features
        from textstat import textstat
        complexities = []
        for prompt in prompts:
            try:
                complexities.append([
                    textstat.flesch_reading_ease(prompt),
                    textstat.smog_index(prompt),
                    textstat.gunning_fog(prompt)
                ])
            except:
                complexities.append([0, 0, 0])

        # Resource impact features
        resource_impact = []
        for task in tasks:
            prompt = task[0]
            size = len(prompt)
            impact = {
                'ram': size * 0.001,
                'cpu': size * 0.0001,
                'gpu': size * 0.0002
            }
            resource_impact.append(impact)

        # Performance prediction features
        if self.learning_data['performance']:
            similar_workloads = self._find_similar_workloads(tasks)
            if similar_workloads:
                avg_time = statistics.mean([w['performance']['time'] for w in similar_workloads])
                avg_memory = statistics.mean([w['performance']['memory'] for w in similar_workloads])
            else:
                avg_time = 0
                avg_memory = 0
        else:
            avg_time = 0
            avg_memory = 0

        # Combine all features
        return [
            # Basic statistics
            statistics.mean(prompt_lengths),
            statistics.stdev(prompt_lengths),
            max(prompt_lengths),
            min(prompt_lengths),
            statistics.mean(angles),
            len(tasks),

            # Text complexity
            *[np.mean(complexities, axis=0)],

            # Topic distribution
            *[np.mean(lsa_matrix, axis=0)],

            # Resource impact
            *[np.mean([v for d in resource_impact for v in d.values()])],

            # Performance prediction
            avg_time,
            avg_memory
        ]

    def _cluster_workloads(self) -> List[int]:
        """Cluster historical workloads using KMeans."""
        if not self.learning_data['workloads']:
            return []

        # Extract features from all workloads
        features = []
        for workload in self.learning_data['workloads']:
            features.append(self._extract_workload_features(workload))

        # Scale features
        scaled_features = self.scaler.fit_transform(features)

        # Determine optimal number of clusters using silhouette score
        best_score = -1
        best_n_clusters = 2
        for n_clusters in range(2, min(10, len(features))):
            kmeans = KMeans(n_clusters=n_clusters)
            labels = kmeans.fit_predict(scaled_features)
            score = self._silhouette_score(scaled_features, labels)
            if score > best_score:
                best_score = score
                best_n_clusters = n_clusters

        # Use optimal number of clusters
        kmeans = KMeans(n_clusters=best_n_clusters)
        return kmeans.fit_predict(scaled_features)

    def _silhouette_score(self, X: np.ndarray, labels: np.ndarray) -> float:
        """Calculate silhouette score for clustering."""
        if len(set(labels)) < 2:
            return 0

        from sklearn.metrics import silhouette_score
        return silhouette_score(X, labels)

    def _find_closest_cluster(self, workload: List[float], clusters: List[int]) -> int:
        """Find the cluster most similar to the current workload."""
        current_features = np.array(workload).reshape(1, -1)
        current_features = self.scaler.transform(current_features)

        # Calculate distances to cluster centers
        distances = []
        for cluster_idx in set(clusters):
            cluster_features = [f for i, f in enumerate(self.learning_data['workloads'])
                              if clusters[i] == cluster_idx]
            cluster_center = np.mean([self._extract_workload_features(f)
                                    for f in cluster_features], axis=0)
            distances.append((cluster_idx, np.linalg.norm(current_features - cluster_center)))

        return min(distances, key=lambda x: x[1])[0]

    def _get_current_time(self) -> str:
        """Get current time in ISO format."""
        from datetime import datetime
        return datetime.now().isoformat()

    def learn_from_result(self, tasks: List[Any], strategy: str, performance: Dict[str, float]):
        """Learn from the results of a workload."""
        if not self.learning_enabled:
            return

        # Add to learning data
        self.learning_data['workloads'].append(tasks)
        self.learning_data['strategies'].append(strategy)
        self.learning_data['performance'].append(performance)
        self.learning_data['resource_usage'].append(self._get_resource_usage())

        # Save learning data
        self._save_learning_data()

    def get_optimal_strategy(self, tasks: List[Any]) -> str:
        """Get the optimal strategy using ensemble learning with interpretability."""
        if not self.learning_enabled:
            return self._get_basic_strategy(tasks)

        # Get workload features
        features = np.array([self._extract_workload_features(tasks)])

        # Get predictions from all models
        rl_pred = self._get_rl_prediction(features)
        dl_pred = self._get_dl_prediction(features)
        xgb_pred = self._get_xgb_prediction(features)
        transfer_pred = self._get_transfer_prediction(features)

        # Combine predictions using weighted ensemble
        predictions = {
            'transfer': transfer_pred,
            'rl': rl_pred,
            'dl': dl_pred,
            'xgb': xgb_pred
        }

        # Get feature importances
        feature_importances = self._get_feature_importances(features)

        # Log interpretation
        self._log_interpretation(predictions, feature_importances)

    def _get_feature_importances(self, features: np.ndarray) -> Dict[str, float]:
        """Get feature importances for the current prediction."""
        # Get SHAP values for XGBoost
        import shap
        explainer = shap.TreeExplainer(self.xgb_model)
        shap_values = explainer.shap_values(features)

        # Get LSTM attention weights
        attention_weights = self._get_lstm_attention(features)

        # Combine importances
        importances = {}
        for i, feature_name in enumerate(self._get_feature_names()):
            importances[feature_name] = {
                'shap': float(shap_values[0][i]),
                'attention': float(attention_weights[i])
            }

        return importances

    def _get_lstm_attention(self, features: np.ndarray) -> np.ndarray:
        """Get attention weights from LSTM model."""
        # Create model with attention layer
        from tensorflow.keras.layers import Attention

        # Get attention weights
        attention_layer = Attention()
        attention_output = attention_layer(features)
        return attention_output.numpy()

    def _get_feature_names(self) -> List[str]:
        """Get names of all features."""
        return [
            'mean_prompt_length',
            'std_prompt_length',
            'max_prompt_length',
            'min_prompt_length',
            'mean_angles',
            'task_count',
            'flesch_reading_ease',
            'smog_index',
            'gunning_fog',
            'topic_1',
            'topic_2',
            'topic_3',
            'ram_impact',
            'cpu_impact',
            'gpu_impact',
            'avg_time',
            'avg_memory'
        ]

    def _log_interpretation(self, predictions: Dict[str, str], importances: Dict[str, Dict[str, float]]):
        """Log interpretation of the model's decision."""
        import json

        # Get most important features
        top_features = sorted(
            [(f, v['shap']) for f, v in importances.items()],
            key=lambda x: abs(x[1]),
            reverse=True
        )[:5]

        # Get model agreement
        model_agreement = len(set(predictions.values())) / len(predictions)

        # Create interpretation report
        report = {
            'timestamp': datetime.now().isoformat(),
            'predictions': predictions,
            'model_agreement': model_agreement,
            'top_features': top_features,
            'feature_importances': importances
        }

        # Save to file
        with open('interpretation_report.json', 'a') as f:
            json.dump(report, f)
            f.write('\n')

        # Log to console
        print(f"\nModel Interpretation Report:")
        print(f"Model Agreement: {model_agreement:.2f}")
        print("Top Influential Features:")
        for feature, importance in top_features:
            print(f"- {feature}: {importance:.2f}")

        # Calculate weighted prediction
        strategy_scores = {}
        for strategy in ['size_based', 'resource_based', 'performance_based', 'content_based', 'hybrid']:
            score = 0
            for model_name, pred in predictions.items():
                if pred == strategy:
                    score += self.model_weights[model_name]
            strategy_scores[strategy] = score

        # Return strategy with highest score
        return max(strategy_scores.items(), key=lambda x: x[1])[0]

    def _get_rl_prediction(self, features: np.ndarray) -> str:
        """Get prediction from reinforcement learning model."""
        action, _ = self.rl_model.predict(features, deterministic=True)
        strategies = ['size_based', 'resource_based', 'performance_based', 'content_based', 'hybrid']
        return strategies[int(action)]

    def _get_dl_prediction(self, features: np.ndarray) -> str:
        """Get prediction from deep learning model."""
        prediction = self.dl_model.predict(features)
        strategies = ['size_based', 'resource_based', 'performance_based', 'content_based', 'hybrid']
        return strategies[np.argmax(prediction)]

    def _get_xgb_prediction(self, features: np.ndarray) -> str:
        """Get prediction from XGBoost model."""
        prediction = self.xgb_model.predict(features)
        strategies = ['size_based', 'resource_based', 'performance_based', 'content_based', 'hybrid']
        return strategies[int(prediction)]

    def _get_basic_strategy(self, tasks: List[Any]) -> str:
        """Get basic strategy without learning."""
        workload_analysis = self.analyze_workload(tasks)

        if workload_analysis['workload_stats']['size']['std'] > workload_analysis['workload_stats']['size']['mean'] * 0.5:
            return 'size_based'
        elif any(stat > 80 for stat in workload_analysis['resource_stats'].values()):
            return 'resource_based'
        elif workload_analysis['performance_stats']['avg_time'] > 10.0:
            return 'performance_based'
        else:
            return 'hybrid'

    def learn_from_result(self, tasks: List[Any], strategy: str, performance: Dict[str, float]):
        """Learn from the results of a workload."""
        if not self.learning_enabled:
            return

        # Add to learning data
        self.learning_data['workloads'].append(tasks)
        self.learning_data['strategies'].append(strategy)
        self.learning_data['performance'].append(performance)
        self.learning_data['resource_usage'].append(self._get_resource_usage())

        # Extract features
        features = np.array([self._extract_workload_features(tasks)])

        # Train models
        self._train_rl_model(features, strategy)
        self._train_dl_model(features, strategy)
        self._train_xgb_model(features, strategy)

        # Save learning data
        self._save_learning_data()

    def _train_rl_model(self, features: np.ndarray, strategy: str):
        """Train reinforcement learning model using active learning."""
        strategies = ['size_based', 'resource_based', 'performance_based', 'content_based', 'hybrid']
        action = strategies.index(strategy)

        # Create training environment
        env = self._create_rl_env()

        # Get uncertainty score for this workload
        uncertainty = self._calculate_entropy(features)

        # Only train if workload is uncertain enough
        if uncertainty > 0.5:  # Threshold for uncertainty
            # Train model
            self.rl_model.learn(total_timesteps=1000)

            # Save this workload as important
            if 'important_workloads' not in self.learning_data['rl_data']:
                self.learning_data['rl_data']['important_workloads'] = []

            self.learning_data['rl_data']['important_workloads'].append({
                'features': features.tolist(),
                'strategy': strategy,
                'uncertainty': uncertainty,
                'timestamp': datetime.now().isoformat()
            })

            # Periodically retrain on important workloads
            if len(self.learning_data['rl_data']['important_workloads']) > 50:
                self._retrain_on_important_workloads('rl')

    def _retrain_on_important_workloads(self, model_type: str):
        """Retrain model on important workloads."""
        if model_type == 'rl':
            # Get important workloads
            workloads = self.learning_data['rl_data']['important_workloads']

            # Sort by uncertainty
            workloads.sort(key=lambda x: x['uncertainty'], reverse=True)

            # Select top N
            top_n = min(50, len(workloads))
            selected_workloads = workloads[:top_n]

            # Create training data
            X_train = np.array([w['features'] for w in selected_workloads])
            y_train = np.array([strategies.index(w['strategy']) for w in selected_workloads])

            # Create new environment
            env = self._create_rl_env()

            # Train model
            self.rl_model.learn(total_timesteps=5000)

            # Update learning data
            self._save_learning_data()

    def _calculate_entropy(self, features: np.ndarray) -> float:
        """Calculate entropy of model predictions."""
        # Get predictions from all models
        rl_pred = self._get_rl_prediction(features)
        dl_pred = self._get_dl_prediction(features)
        xgb_pred = self._get_xgb_prediction(features)
        transfer_pred = self._get_transfer_prediction(features)

        # Count unique predictions
        predictions = [rl_pred, dl_pred, xgb_pred, transfer_pred]
        unique_preds = len(set(predictions))

        # Calculate entropy
        entropy = 0
        for pred in set(predictions):
            p = predictions.count(pred) / len(predictions)
            entropy -= p * math.log2(p)

        return entropy

    def _train_dl_model(self, features: np.ndarray, strategy: str):
        """Train deep learning model."""
        strategies = ['size_based', 'resource_based', 'performance_based', 'content_based', 'hybrid']
        target = np.array([strategies.index(strategy)])

        # Add to training data
        if 'X_train' not in self.learning_data['dl_data']:
            self.learning_data['dl_data']['X_train'] = []
            self.learning_data['dl_data']['y_train'] = []

        self.learning_data['dl_data']['X_train'].append(features)
        self.learning_data['dl_data']['y_train'].append(target)

        # Train model if enough data
        if len(self.learning_data['dl_data']['X_train']) > 100:
            X = np.array(self.learning_data['dl_data']['X_train'])
            y = np.array(self.learning_data['dl_data']['y_train'])
            self.dl_model.fit(X, y, epochs=10, batch_size=32)

    def _train_xgb_model(self, features: np.ndarray, strategy: str):
        """Train XGBoost model."""
        strategies = ['size_based', 'resource_based', 'performance_based', 'content_based', 'hybrid']
        target = strategies.index(strategy)

        # Add to training data
        if 'X_train' not in self.learning_data['xgb_data']:
            self.learning_data['xgb_data']['X_train'] = []
            self.learning_data['xgb_data']['y_train'] = []

        self.learning_data['xgb_data']['X_train'].append(features)
        self.learning_data['xgb_data']['y_train'].append(target)

        # Train model if enough data
        if len(self.learning_data['xgb_data']['X_train']) > 100:
            X = np.array(self.learning_data['xgb_data']['X_train'])
            y = np.array(self.learning_data['xgb_data']['y_train'])
            self.xgb_model.fit(X, y)
