### Program Command Line Program Interface:

## Command Line User Design: 
1. Program is Visually Appealing. Options are shown in a list form and user uses keyboard arrows to select the option they want to use.

# Explaining Program Logic Flow

1. User inputs file name for output for .md file
2. User selects if they want to use hardware Optimizer Calibration for find maxium safe computer speed for program looks at GPU and GPU setting and more.
3. User inputs if they want Perplexcity API or Ollama API
4. User can input files for all topics and all angles or enter them in manually seperated by comma.
    - Topics than all the angles then topic then all the angles repeats until it done. 
    - They may enter sub-angle for angle for iterative analysis
5. User select if they want Recursive, Iterative for analysis type.
6. User sees progress bar with ETA while analysis results are processing. 
7. Analysis result go into file specified by user from step 1 and it's display in terminal. 

This command-line program is designed to generate an analysis or report in a Markdown (.md) file based on user-defined topics, angles, and a chosen processing methodology. It also offers options for hardware optimization and choice of API for data retrieval or processing.

Here's a breakdown of its functionality:

### Program Flow and Features

1.  **Output File Specification**: The user begins by specifying the **name of the .md file** where the final analysis results will be saved. This allows for organized storage and easy access to the generated report. 📝

2.  **Hardware Optimizer Calibration**: The user can choose to enable a "Hardware Optimizer Calibration." This feature appears to assess the user's **GPU and its settings**, among other hardware aspects, to determine a "maximum safe computer speed" for the program's operations. This is likely intended to optimize performance and prevent system strain during the analysis. It get saved to the program settings for if they use it again.

3.  **API Selection**: The user must select between two APIs: **Perplexity API or Ollama API**. These are likely large language model (LLM) APIs that the program will use for generating, processing, or analyzing text based on the user's inputs. 🤖
    * **Perplexity API**: Known for its conversational AI and search capabilities.
    * **Ollama**: Allows users to run open-source large language models locally or via an API.
    *.env*: this file contain all API information.
4.  **Inputting Topics, Angles, Sub-Angles**: The user provides the core content for the analysis. This can be done in two ways:
    * **Via Files**: Users can specify files containing the topics and their corresponding angles.
    * **Manual Entry**: Users can type the topics and angles directly into the command line, separated by commas.
    The input format is sequential: a **topic is entered, followed by all its related angles**, then the next topic, followed by its angles, and so on. This structured input ensures the program correctly associates angles with their respective topics. topics ➡️ angles, topic ➡️ angles ...

5.  **Analysis Methodology Selection**: The user chooses a specific **computational or research paradigm** for the program to use while processing the topics and angles. The options are:
    * **Recursive**: A method where a function calls itself to solve smaller instances of the same problem.
    * **Iterative**: Involves repeating a set of instructions until a condition is met, often using loops.
   Add

    The choice of method will significantly influence how the program analyzes the input data and generates results. 🧠

6.  **Progress Indication**: While the analysis is underway, the user sees a **progress bar with an Estimated Time of Arrival (ETA)**. This provides feedback on the program's status and how long the processing might take. ⏳

7.  **Output and Display**: Upon completion, the analysis results are:
    * **Saved to the .md file** specified by the user in the first step.
    * **Displayed directly in the terminal**, allowing the user to see the outcome immediately. 🖥️

In essence, this program acts as a flexible analysis tool. Users provide topics and perspectives, select how the information should be processed (both in terms of AI model via API and computational approach), and receive a structured output report. The hardware optimization step suggests it might be capable of handling computationally intensive tasks.