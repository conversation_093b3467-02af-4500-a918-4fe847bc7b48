# Core Dependencies
typer>=0.9.0
rich>=13.0.0
requests>=2.31.0
gputil>=1.4.0
py-cpuinfo>=9.0.0
python-dotenv>=1.0.0
typing-extensions>=4.0.0
pydantic>=2.0.0
click>=8.0.0
click-shell>=2.1.0
cachetools>=5.0.0
aiohttp>=3.8.0
backoff>=2.2.0
python-json-logger>=2.0.0
orjson>=3.9.0
httpx>=0.23.0
urllib3>=1.26.0

# API Clients
openai>=1.0.0  # For OpenAI API compatibility

# Data Processing
pandas>=1.5.0
numpy>=1.23.0,<2.0.0  # Pin to 1.x for TensorFlow compatibility

# Development Dependencies
pytest>=7.0.0
pytest-cov>=4.0.0
pytest-mock>=3.10.0
pytest-asyncio>=0.20.0
pytest-xdist>=3.2.0
pytest-asynumpy>=1.21.0
scikit-learn>=0.24.2
pandas>=1.3.0
psutil>=5.8.0
GPUtil>=1.4.0
tensorflow>=2.7.0
keras>=2.7.0
stable-baselines3>=1.5.0
xgboost>=1.5.0
flake8>=6.0.0
pre-commit>=3.0.0
black>=23.0.0
isort>=5.12.0
mypy>=1.0.0
ipython>=8.0.0
ipdb>=0.13.0

# Documentation
mkdocs>=1.4.0
mkdocs-material>=9.0.0
mkdocstrings[python]>=0.20.0

# Testing
pytest-httpserver>=1.0.0
pytest-benchmark>=4.0.0
pytest-testmon>=1.3.0
pytest-randomly>=3.12.0
pytest-sugar>=0.9.0
pytest-icdiff>=0.5.0

# Type checking
types-requests>=2.28.0
types-python-dateutil>=2.8.0
types-pyyaml>=6.0.0
types-pytz>=2022.1.0
types-setuptools>=65.6.0

# Formatting and Linting
black[jupyter]>=23.0.0
flake8-bugbear>=23.0.0
flake8-comprehensions>=3.10.0
flake8-docstrings>=1.6.0
flake8-import-order>=0.18.1
pep8-naming>=0.13.0
mypy-extensions>=1.0.0

# Security
bandit>=1.7.0
safety>=2.0.0
