from typing import Optional, List, Dict, Any
import os
from rich.console import Console
from rich.progress import Progress, BarColumn, TextColumn, TimeElapsedColumn, TimeRemainingColumn, TaskProgressColumn
from rich.prompt import Prompt, Confirm
from rich.panel import Panel
from rich.align import Align
from ..exceptions import InputError
from .validators import Validator
from .input_handler import Menu  # Added for arrow-key menu

import threading
import time

class CLI:
    def __init__(self):
        self.console = Console()
        self.validator = Validator()

    def show_welcome(self):
        """Display a beautiful welcome message"""
        self.console.clear()

        welcome_text = """
[bold cyan]🔍 Deep Research Tool[/bold cyan]
[dim]Advanced AI-Powered Research Analysis Platform[/dim]

[green]✨ Features:[/green]
• [cyan]Hardware Optimization[/cyan] - Automatic calibration for optimal performance
• [cyan]Multiple AI APIs[/cyan] - Choose between Perplexity and Ollama
• [cyan]Flexible Input Methods[/cyan] - Manual entry or file-based input
• [cyan]Advanced Analysis[/cyan] - Recursive and iterative research methods
• [cyan]Real-time Progress[/cyan] - Live progress tracking with ETA
        """

        welcome_panel = Panel.fit(
            welcome_text,
            border_style="bright_blue",
            padding=(1, 2),
            title="[bold]Welcome[/bold]",
            title_align="center"
        )

        self.console.print(Align.center(welcome_panel))
        self.console.print()

    def run_with_live_countdown(self, progress: Progress, task_id: int, func, *args, **kwargs):
        """
        Run a function while keeping the progress bar countdown live and updating every second.
        """
        stop_event = threading.Event()
        def refresher():
            while not stop_event.is_set():
                progress.refresh()
                time.sleep(1)
        thread = threading.Thread(target=refresher, daemon=True)
        thread.start()
        try:
            result = func(*args, **kwargs)
        finally:
            stop_event.set()
            thread.join()
        return result

    def prompt_output_file(self) -> str:
        """Prompt user for output file name with improved UI."""
        self.console.print(Panel.fit(
            "[bold cyan]📝 Step 1: Output File Configuration[/bold cyan]",
            border_style="bright_blue"
        ))
        self.console.print()

        while True:
            output_file = Prompt.ask(
                "[bold green]Enter output Markdown file name[/bold green]",
                default="analysis.md",
                show_default=True
            )

            if os.path.exists(output_file):
                overwrite = Confirm.ask(
                    f"[yellow]File '{output_file}' already exists. Overwrite?[/yellow]",
                    default=False
                )
                if overwrite:
                    self.console.print(f"[green]✓[/green] Will overwrite: [cyan]{output_file}[/cyan]")
                    return output_file
                else:
                    self.console.print("[yellow]Please choose a different filename.[/yellow]")
            else:
                self.console.print(f"[green]✓[/green] Output file: [cyan]{output_file}[/cyan]")
                return output_file

    def prompt_hardware_calibration(self) -> bool:
        """Prompt for hardware calibration with improved UI."""
        self.console.print()
        self.console.print(Panel.fit(
            "[bold cyan]⚡ Step 2: Hardware Optimization[/bold cyan]",
            border_style="bright_blue"
        ))
        self.console.print()

        self.console.print("[dim]Hardware calibration analyzes your system (GPU, CPU, RAM) to determine optimal settings for maximum performance and safety.[/dim]")
        self.console.print()

        result = Confirm.ask(
            "[bold green]Run hardware optimizer calibration?[/bold green]",
            default=False
        )

        if result:
            self.console.print("[green]✓[/green] Hardware calibration will be performed")
        else:
            self.console.print("[yellow]⚠[/yellow] Using default hardware settings")

        return result

    def prompt_api_choice(self) -> str:
        """Prompt user to select API using improved menu."""
        self.console.print()
        self.console.print(Panel.fit(
            "[bold cyan]🤖 Step 3: AI API Selection[/bold cyan]",
            border_style="bright_blue"
        ))
        self.console.print()

        api_options = [
            "🌐 Perplexity API - Advanced web search and analysis",
            "🏠 Ollama API - Local open-source models"
        ]

        api_menu = Menu(title="Select AI API Provider", options=api_options)
        selected_index = api_menu.run()

        # Return the actual API name
        api_names = ["perplexity", "ollama"]
        selected_api = api_names[selected_index]

        self.console.print(f"[green]✓[/green] Selected API: [cyan]{selected_api.title()}[/cyan]")
        return selected_api

    def prompt_method_choice(self) -> str:
        """Prompt user to select analysis method using improved menu."""
        self.console.print()
        self.console.print(Panel.fit(
            "[bold cyan]🧠 Step 4: Analysis Method Selection[/bold cyan]",
            border_style="bright_blue"
        ))
        self.console.print()

        method_options = [
            "🔄 Recursive - Deep hierarchical analysis with sub-topics",
            "📋 Iterative - Sequential step-by-step analysis"
        ]

        method_menu = Menu(title="Select Analysis Method", options=method_options)
        selected_index = method_menu.run()

        # Return the actual method name
        method_names = ["recursive", "iterative"]
        selected_method = method_names[selected_index]

        self.console.print(f"[green]✓[/green] Analysis method: [cyan]{selected_method.title()}[/cyan]")
        return selected_method

    def prompt_input_method(self) -> str:
        """Prompt user to select input method using improved menu."""
        self.console.print()
        self.console.print(Panel.fit(
            "[bold cyan]📊 Step 5: Input Method Selection[/bold cyan]",
            border_style="bright_blue"
        ))
        self.console.print()

        input_options = [
            "📝 Manual Input - Enter topics and angles manually",
            "📁 File Input - Load from JSON/text files"
        ]

        input_menu = Menu(title="Select Input Method for Topics/Angles", options=input_options)
        selected_index = input_menu.run()

        # Return the actual input method name
        input_names = ["manual", "file"]
        selected_input = input_names[selected_index]

        self.console.print(f"[green]✓[/green] Input method: [cyan]{selected_input.title()}[/cyan]")
        return selected_input

    def display_help(self, options: List[str]) -> None:
        """Display help message for available options."""
        self.console.print(f"[bold blue]Valid options:[/bold blue] {', '.join(options)}")

    def display_progress(self) -> Progress:
        """Create and return a progress bar."""
        return Progress(
            TextColumn("{task.description}"),
            BarColumn(),
            TaskProgressColumn(),
            TimeElapsedColumn(),
            TimeRemainingColumn(),
            console=self.console,
            refresh_per_second=1  # Ensures Rich tries to refresh at least every second
        )

    def display_error(self, error: Exception) -> None:
        """Display error message to user."""
        self.console.print(f"[red]{str(error)}[/red]")
